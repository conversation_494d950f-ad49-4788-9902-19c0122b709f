import React, { useState } from 'react';
import { Navigation } from './Navigation';
import { DashboardGallery } from './DashboardGallery';
import { MyDashboards } from './MyDashboards';
import { TrainingPage } from './TrainingPage';
import { SettingsPage } from './SettingsPage';

export const DashboardSharingTool: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('gallery');
  const [connectionState, setConnectionState] = useState<{
    isConnected: boolean;
    grantKey?: string;
    organization?: { id: string; name: string };
  }>({ isConnected: false });

  const handleConnectionChange = (isConnected: boolean, grantKey?: string, organization?: { id: string; name: string }) => {
    setConnectionState({ isConnected, grantKey, organization });
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'gallery':
        return <DashboardGallery connectionState={connectionState} />;
      case 'dashboards':
        return <MyDashboards connectionState={connectionState} />;
      case 'training':
        return <TrainingPage />;
      case 'settings':
        return <SettingsPage connectionState={connectionState} onConnectionChange={handleConnectionChange} />;
      default:
        return <DashboardGallery connectionState={connectionState} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-blue-600 text-white">
        <div className="container mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold">JobTread Dashboard Sharing Tool</h1>
          <p className="text-blue-100 mt-2">
            Import, export, and manage your JobTread dashboards with ease
          </p>
        </div>
      </header>

      {/* Navigation */}
      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {renderContent()}
      </main>
    </div>
  );
};