import React from 'react';
import { Info, AlertTriangle, CheckCircle } from 'lucide-react';

interface InfoCardProps {
  title: string;
  content: string;
  type?: 'info' | 'warning' | 'success';
}

export const InfoCard: React.FC<InfoCardProps> = ({
  title,
  content,
  type = 'info'
}) => {
  const icons = {
    info: <Info className="h-5 w-5 text-blue-500" />,
    warning: <AlertTriangle className="h-5 w-5 text-amber-500" />,
    success: <CheckCircle className="h-5 w-5 text-green-500" />
  };

  const bgColors = {
    info: 'bg-blue-50 border-blue-200',
    warning: 'bg-amber-50 border-amber-200',
    success: 'bg-green-50 border-green-200'
  };

  const titleColors = {
    info: 'text-blue-800',
    warning: 'text-amber-800',
    success: 'text-green-800'
  };

  return (
    <div className={`p-4 rounded-lg border ${bgColors[type]}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0 mt-0.5">
          {icons[type]}
        </div>
        <div className="ml-3">
          <h3 className={`text-sm font-medium ${titleColors[type]}`}>{title}</h3>
          <div className="mt-2 text-sm text-gray-600">
            {content}
          </div>
        </div>
      </div>
    </div>
  );
};